'use client';

import { useCallback, useEffect, useMemo, useState } from 'react';

import { getUsersWithPagination, searchUsers } from '@/api/user-api';
import { Pagination } from '@/components/ui/pagination';
import type { SortDirection } from '@/components/ui/sortable-table-header';
import type { UserEntity } from '@/core.constants';
import { usePagePagination } from '@/hooks/use-page-pagination';

import { UsersHeader } from './users-management/users-header';
import { UsersTable } from './users-management/users-table';

export function UsersManagement() {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortKey, setSortKey] = useState<string>('');
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);
  const [searchResults, setSearchResults] = useState<UserEntity[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  const fetchUsersPage = useCallback(async (page: number, pageSize: number) => {
    return getUsersWithPagination(page, pageSize);
  }, []);

  const {
    items: users,
    loading,
    currentPage,
    totalPages,
    totalItems,
    goToPage,
    loadPage,
    refresh,
  } = usePagePagination<UserEntity>(fetchUsersPage, {
    pageSize: 25,
  });

  useEffect(() => {
    loadPage(1);
  }, [loadPage]);

  const handlePageChange = (page: number) => {
    goToPage(page);
  };

  const handleSearchChange = useCallback(async (term: string) => {
    setSearchTerm(term);

    if (!term.trim()) {
      setSearchResults([]);
      setIsSearching(false);
      return;
    }

    setIsSearching(true);
    try {
      const results = await searchUsers(term);
      setSearchResults(results);
    } catch (error) {
      console.error('Error searching users:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, []);

  const handleSort = useCallback((key: string, direction: SortDirection) => {
    setSortKey(key);
    setSortDirection(direction);
  }, []);

  const displayUsers = useMemo(() => {
    const usersToSort = searchTerm ? searchResults : users;

    if (!sortKey || !sortDirection) {
      return usersToSort;
    }

    return [...usersToSort].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      if (sortKey === 'displayName') {
        aValue = (a.displayName || a.name || '').toLowerCase();
        bValue = (b.displayName || b.name || '').toLowerCase();
      } else if (sortKey === 'balance') {
        aValue = a.balance?.sum || 0;
        bValue = b.balance?.sum || 0;
      } else {
        return 0;
      }

      if (sortDirection === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });
  }, [users, searchResults, searchTerm, sortKey, sortDirection]);

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <UsersHeader
          onRefresh={refresh}
          totalUsers={searchTerm ? searchResults.length : totalItems}
        />

        <UsersTable
          users={displayUsers}
          loading={loading || isSearching}
          searchTerm={searchTerm}
          onSearchChange={handleSearchChange}
          sortKey={sortKey}
          sortDirection={sortDirection}
          onSort={handleSort}
        />

        {!searchTerm && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            className="mt-4"
          />
        )}
      </div>
    </div>
  );
}
