'use client';

import { useCallback, useEffect, useState } from 'react';

import {
  getUsersWithPagination,
  type GetUsersWithPaginationParams,
  searchUsers,
} from '@/api/user-api';
import { Pagination } from '@/components/ui/pagination';
import type { UserEntity } from '@/core.constants';
import { usePagePagination } from '@/hooks/use-page-pagination';
import { useTableSorting } from '@/hooks/use-table-sorting';

import { UsersHeader } from './users-management/users-header';
import { UsersTable } from './users-management/users-table';

export function UsersManagement() {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<UserEntity[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  const { sortKey, sortDirection, handleSort } = useTableSorting({
    defaultSortKey: 'displayName',
    defaultSortDirection: 'asc',
  });

  const fetchUsersPage = useCallback(
    async (page: number, pageSize: number) => {
      const params: GetUsersWithPaginationParams = {
        page,
        pageSize,
        sortBy: sortKey || 'displayName',
        sortDirection: sortDirection || 'asc',
        searchTerm: searchTerm || undefined,
      };
      return getUsersWithPagination(params);
    },
    [sortKey, sortDirection, searchTerm],
  );

  const {
    items: users,
    loading,
    currentPage,
    totalPages,
    totalItems,
    goToPage,
    loadPage,
    refresh,
  } = usePagePagination<UserEntity>(fetchUsersPage, {
    pageSize: 25,
  });

  useEffect(() => {
    loadPage(1);
  }, [loadPage]);

  // Reload data when sorting changes
  useEffect(() => {
    if (sortKey && sortDirection) {
      refresh();
    }
  }, [sortKey, sortDirection, refresh]);

  const handlePageChange = (page: number) => {
    goToPage(page);
  };

  const handleSearchChange = useCallback(async (term: string) => {
    setSearchTerm(term);

    if (!term.trim()) {
      setSearchResults([]);
      setIsSearching(false);
      return;
    }

    setIsSearching(true);
    try {
      const results = await searchUsers(term);
      setSearchResults(results);
    } catch (error) {
      console.error('Error searching users:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, []);

  const displayUsers = searchTerm ? searchResults : users;

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <UsersHeader
          onRefresh={refresh}
          totalUsers={searchTerm ? searchResults.length : totalItems}
        />

        <UsersTable
          users={displayUsers}
          loading={loading || isSearching}
          searchTerm={searchTerm}
          onSearchChange={handleSearchChange}
          sortKey={sortKey}
          sortDirection={sortDirection}
          onSort={handleSort}
        />

        {!searchTerm && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            className="mt-4"
          />
        )}
      </div>
    </div>
  );
}
