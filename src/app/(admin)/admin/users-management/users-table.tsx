import { Avatar } from '@telegram-apps/telegram-ui';
import { <PERSON><PERSON>, Loader2, Search, User } from 'lucide-react';

import {
  SortableTableHeader,
  type SortDirection,
} from '@/components/ui/sortable-table-header';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import type { UserEntity } from '@/core.constants';
import { getBalanceInfo } from '@/services/user-service';

interface UsersTableProps {
  users: UserEntity[];
  loading: boolean;
  searchTerm: string;
  onSearchChange: (term: string) => void;
  sortKey?: string;
  sortDirection?: SortDirection;
  onSort: (key: string, direction: SortDirection) => void;
}

export function UsersTable({
  users,
  loading,
  searchTerm,
  onSearchChange,
  sortKey,
  sortDirection,
  onSort,
}: UsersTableProps) {
  const formatTonAddress = (address?: string) => {
    if (!address) return 'Not connected';
    return `${address.slice(0, 6)}...${address.slice(-6)}`;
  };

  const formatTelegramHandle = (handle?: string) => {
    if (!handle) return '-';
    return `@${handle}`;
  };

  const formatBalance = (user: UserEntity) => {
    if (!user.balance) return '0.00 TON (0.00)';
    const { availableBalance } = getBalanceInfo(user);
    const lockedBalance = user.balance.locked || 0;
    return `${availableBalance.toFixed(2)} TON (${lockedBalance.toFixed(2)})`;
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      // You might want to add a toast notification here
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="w-6 h-6 animate-spin text-[#6ab2f2]" />
        <span className="ml-2 text-[#708499]">Loading users...</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#708499] w-4 h-4" />
        <Input
          placeholder="Search by name or telegram handle..."
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="pl-10 bg-[#232e3c] border-[#3a4a5c] text-white placeholder:text-[#708499]"
        />
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Avatar</TableHead>
              <SortableTableHeader
                sortKey="displayName"
                currentSortKey={sortKey}
                currentSortDirection={sortDirection}
                onSort={onSort}
              >
                Display Name
              </SortableTableHeader>
              <TableHead>Telegram Handle</TableHead>
              <TableHead>TON Address</TableHead>
              <SortableTableHeader
                sortKey="balance"
                currentSortKey={sortKey}
                currentSortDirection={sortDirection}
                onSort={onSort}
              >
                Balance
              </SortableTableHeader>
            </TableRow>
          </TableHeader>
          <TableBody>
            {users.map((user) => (
              <TableRow key={user.id}>
                <TableCell>
                  <div className="w-10 h-10 rounded-full overflow-hidden bg-[#232e3c] flex items-center justify-center">
                    {user.photoURL ? (
                      <Avatar size={40} src={user.photoURL} />
                    ) : (
                      <User className="w-5 h-5 text-[#708499]" />
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex flex-col">
                    <span className="font-medium">
                      {user.displayName || user.name || 'Anonymous User'}
                    </span>
                    {user.tg_id && (
                      <span className="text-xs text-[#708499]">
                        ID: {user.tg_id}
                      </span>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <span className="text-sm">
                    {formatTelegramHandle(user.telegram_handle)}
                  </span>
                </TableCell>
                <TableCell>
                  {user.ton_wallet_address ? (
                    <button
                      onClick={() => copyToClipboard(user.ton_wallet_address!)}
                      className="flex items-center gap-2 text-sm font-mono hover:text-[#6ab2f2] transition-colors group cursor-pointer"
                      title="Click to copy address"
                    >
                      <span>{formatTonAddress(user.ton_wallet_address)}</span>
                      <Copy className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity" />
                    </button>
                  ) : (
                    <span className="text-sm font-mono">Not connected</span>
                  )}
                </TableCell>
                <TableCell>
                  <span className="text-sm font-mono text-[#6ab2f2]">
                    {formatBalance(user)}
                  </span>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
